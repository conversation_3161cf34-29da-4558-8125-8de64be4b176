"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/BaseNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BaseNode(param) {\n    let { data, children, icon: Icon, color = '#ff6b35', hasInput = true, hasOutput = true, hasRoleInput = false, inputLabel = 'Input', outputLabel = 'Output', roleInputLabel = 'Role', className = '' } = param;\n    const isConfigured = data.isConfigured;\n    const hasError = data.hasError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-1/2 transform -translate-y-1/2 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"input\",\n                        className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                        style: {\n                            left: -8\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-2 px-2 py-1 bg-gray-800/90 border border-gray-600 rounded text-xs text-gray-300 opacity-0 hover:opacity-100 transition-opacity pointer-events-none\",\n                        children: inputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this),\n            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-1/4 transform -translate-y-1/2 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"role\",\n                        className: \"w-3 h-3 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                        style: {\n                            left: -6\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-1 px-2 py-1 bg-purple-900/90 border border-purple-600 rounded text-xs text-purple-200 opacity-0 hover:opacity-100 transition-opacity pointer-events-none\",\n                        children: roleInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 transition-all duration-200 \".concat(hasError ? 'border-red-500 bg-red-900/20' : isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : \"linear-gradient(135deg, \".concat(color, \"20, \").concat(color, \"10)\")\n                        },\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: hasError ? '#ef444420' : \"\".concat(color, \"20\"),\n                                    color: hasError ? '#ef4444' : color\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    data.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: data.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\",\n                                    title: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this) : isConfigured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                    title: \"Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\",\n                                    title: \"Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    (hasInput || hasRoleInput) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 h-full flex flex-col justify-center pointer-events-none\",\n                        children: [\n                            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                style: {\n                                    marginTop: hasRoleInput ? '20%' : '0'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-6 bg-gray-500/50 rounded-r\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-1 text-xs text-gray-400 bg-gray-800/80 px-1 py-0.5 rounded text-center min-w-[40px]\",\n                                        children: \"Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this),\n                            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                style: {\n                                    marginTop: hasInput ? '-10%' : '-25%'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-4 bg-purple-500/50 rounded-r\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-1 text-xs text-purple-300 bg-purple-900/80 px-1 py-0.5 rounded text-center min-w-[40px]\",\n                                        children: \"Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    hasError && data.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-red-300\",\n                            children: data.errorMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            hasOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    right: -8\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21hbnVhbC1idWlsZC9ub2Rlcy9CYXNlTm9kZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFaUQ7QUFrQmxDLFNBQVNFLFNBQVMsS0FZakI7UUFaaUIsRUFDL0JDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxNQUFNQyxJQUFJLEVBQ1ZDLFFBQVEsU0FBUyxFQUNqQkMsV0FBVyxJQUFJLEVBQ2ZDLFlBQVksSUFBSSxFQUNoQkMsZUFBZSxLQUFLLEVBQ3BCQyxhQUFhLE9BQU8sRUFDcEJDLGNBQWMsUUFBUSxFQUN0QkMsaUJBQWlCLE1BQU0sRUFDdkJDLFlBQVksRUFBRSxFQUNBLEdBWmlCO0lBYS9CLE1BQU1DLGVBQWVaLEtBQUtZLFlBQVk7SUFDdEMsTUFBTUMsV0FBV2IsS0FBS2EsUUFBUTtJQUU5QixxQkFDRSw4REFBQ0M7UUFBSUgsV0FBVyxZQUFzQixPQUFWQTs7WUFFekJOLDBCQUNDLDhEQUFDUztnQkFBSUgsV0FBVTs7a0NBQ2IsOERBQUNkLGlEQUFNQTt3QkFDTGtCLE1BQUs7d0JBQ0xDLFVBQVVsQixtREFBUUEsQ0FBQ21CLElBQUk7d0JBQ3ZCQyxJQUFHO3dCQUNIUCxXQUFVO3dCQUNWUSxPQUFPOzRCQUFFQyxNQUFNLENBQUM7d0JBQUU7Ozs7OztrQ0FFcEIsOERBQUNOO3dCQUFJSCxXQUFVO2tDQUNaSDs7Ozs7Ozs7Ozs7O1lBTU5ELDhCQUNDLDhEQUFDTztnQkFBSUgsV0FBVTs7a0NBQ2IsOERBQUNkLGlEQUFNQTt3QkFDTGtCLE1BQUs7d0JBQ0xDLFVBQVVsQixtREFBUUEsQ0FBQ21CLElBQUk7d0JBQ3ZCQyxJQUFHO3dCQUNIUCxXQUFVO3dCQUNWUSxPQUFPOzRCQUFFQyxNQUFNLENBQUM7d0JBQUU7Ozs7OztrQ0FFcEIsOERBQUNOO3dCQUFJSCxXQUFVO2tDQUNaRDs7Ozs7Ozs7Ozs7OzBCQU1QLDhEQUFDSTtnQkFDQ0gsV0FBVyxpRUFNVixPQUxDRSxXQUNJLGlDQUNBRCxlQUNBLG1DQUNBLHNDQUNMO2dCQUNETyxPQUFPO29CQUNMRSxhQUFhUixXQUFXLFlBQVlELGVBQWVSLFFBQVE7Z0JBQzdEOztrQ0FHQSw4REFBQ1U7d0JBQ0NILFdBQVU7d0JBQ1ZRLE9BQU87NEJBQ0xHLFlBQVlULFdBQ1IsNEVBQ0EsMkJBQXVDVCxPQUFaQSxPQUFNLFFBQVksT0FBTkEsT0FBTTt3QkFDbkQ7OzRCQUVDRCxzQkFDQyw4REFBQ1c7Z0NBQ0NILFdBQVU7Z0NBQ1ZRLE9BQU87b0NBQ0xJLGlCQUFpQlYsV0FBVyxjQUFjLEdBQVMsT0FBTlQsT0FBTTtvQ0FDbkRBLE9BQU9TLFdBQVcsWUFBWVQ7Z0NBQ2hDOzBDQUVBLDRFQUFDRDtvQ0FBS1EsV0FBVTs7Ozs7Ozs7Ozs7MENBR3BCLDhEQUFDRztnQ0FBSUgsV0FBVTs7a0RBQ2IsOERBQUNHO3dDQUFJSCxXQUFVO2tEQUNaWCxLQUFLd0IsS0FBSzs7Ozs7O29DQUVaeEIsS0FBS3lCLFdBQVcsa0JBQ2YsOERBQUNYO3dDQUFJSCxXQUFVO2tEQUNaWCxLQUFLeUIsV0FBVzs7Ozs7Ozs7Ozs7OzBDQU12Qiw4REFBQ1g7Z0NBQUlILFdBQVU7MENBQ1pFLHlCQUNDLDhEQUFDQztvQ0FBSUgsV0FBVTtvQ0FBa0NlLE9BQU07Ozs7OzJDQUNyRGQsNkJBQ0YsOERBQUNFO29DQUFJSCxXQUFVO29DQUFvQ2UsT0FBTTs7Ozs7eURBRXpELDhEQUFDWjtvQ0FBSUgsV0FBVTtvQ0FBcUNlLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU05RHJCLENBQUFBLFlBQVlFLFlBQVcsbUJBQ3ZCLDhEQUFDTzt3QkFBSUgsV0FBVTs7NEJBQ1pOLDBCQUNDLDhEQUFDUztnQ0FBSUgsV0FBVTtnQ0FBeUJRLE9BQU87b0NBQUVRLFdBQVdwQixlQUFlLFFBQVE7Z0NBQUk7O2tEQUNyRiw4REFBQ087d0NBQUlILFdBQVU7Ozs7OztrREFDZiw4REFBQ0c7d0NBQUlILFdBQVU7a0RBQXlGOzs7Ozs7Ozs7Ozs7NEJBSzNHSiw4QkFDQyw4REFBQ087Z0NBQUlILFdBQVU7Z0NBQW9CUSxPQUFPO29DQUFFUSxXQUFXdEIsV0FBVyxTQUFTO2dDQUFPOztrREFDaEYsOERBQUNTO3dDQUFJSCxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNHO3dDQUFJSCxXQUFVO2tEQUE2Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVNuSFYsMEJBQ0MsOERBQUNhO3dCQUFJSCxXQUFVO2tDQUNaVjs7Ozs7O29CQUtKWSxZQUFZYixLQUFLNEIsWUFBWSxrQkFDNUIsOERBQUNkO3dCQUFJSCxXQUFVO2tDQUNiLDRFQUFDRzs0QkFBSUgsV0FBVTtzQ0FDWlgsS0FBSzRCLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT3pCdEIsMkJBQ0MsOERBQUNULGlEQUFNQTtnQkFDTGtCLE1BQUs7Z0JBQ0xDLFVBQVVsQixtREFBUUEsQ0FBQytCLEtBQUs7Z0JBQ3hCbEIsV0FBVTtnQkFDVlEsT0FBTztvQkFBRVcsT0FBTyxDQUFDO2dCQUFFOzs7Ozs7Ozs7Ozs7QUFLN0I7S0E1SndCL0IiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxtYW51YWwtYnVpbGRcXG5vZGVzXFxCYXNlTm9kZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBIYW5kbGUsIFBvc2l0aW9uIH0gZnJvbSAnQHh5Zmxvdy9yZWFjdCc7XG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBXb3JrZmxvd05vZGUgfSBmcm9tICdAL3R5cGVzL21hbnVhbEJ1aWxkJztcblxuaW50ZXJmYWNlIEJhc2VOb2RlUHJvcHMge1xuICBkYXRhOiBXb3JrZmxvd05vZGVbJ2RhdGEnXTtcbiAgY2hpbGRyZW4/OiBSZWFjdE5vZGU7XG4gIGljb24/OiBSZWFjdC5Db21wb25lbnRUeXBlPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+O1xuICBjb2xvcj86IHN0cmluZztcbiAgaGFzSW5wdXQ/OiBib29sZWFuO1xuICBoYXNPdXRwdXQ/OiBib29sZWFuO1xuICBoYXNSb2xlSW5wdXQ/OiBib29sZWFuO1xuICBpbnB1dExhYmVsPzogc3RyaW5nO1xuICBvdXRwdXRMYWJlbD86IHN0cmluZztcbiAgcm9sZUlucHV0TGFiZWw/OiBzdHJpbmc7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQmFzZU5vZGUoe1xuICBkYXRhLFxuICBjaGlsZHJlbixcbiAgaWNvbjogSWNvbixcbiAgY29sb3IgPSAnI2ZmNmIzNScsXG4gIGhhc0lucHV0ID0gdHJ1ZSxcbiAgaGFzT3V0cHV0ID0gdHJ1ZSxcbiAgaGFzUm9sZUlucHV0ID0gZmFsc2UsXG4gIGlucHV0TGFiZWwgPSAnSW5wdXQnLFxuICBvdXRwdXRMYWJlbCA9ICdPdXRwdXQnLFxuICByb2xlSW5wdXRMYWJlbCA9ICdSb2xlJyxcbiAgY2xhc3NOYW1lID0gJydcbn06IEJhc2VOb2RlUHJvcHMpIHtcbiAgY29uc3QgaXNDb25maWd1cmVkID0gZGF0YS5pc0NvbmZpZ3VyZWQ7XG4gIGNvbnN0IGhhc0Vycm9yID0gZGF0YS5oYXNFcnJvcjtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgcmVsYXRpdmUgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7LyogSW5wdXQgSGFuZGxlIHdpdGggTGFiZWwgKi99XG4gICAgICB7aGFzSW5wdXQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMCB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgPEhhbmRsZVxuICAgICAgICAgICAgdHlwZT1cInRhcmdldFwiXG4gICAgICAgICAgICBwb3NpdGlvbj17UG9zaXRpb24uTGVmdH1cbiAgICAgICAgICAgIGlkPVwiaW5wdXRcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCBib3JkZXItMiBib3JkZXItZ3JheS01MDAgYmctZ3JheS03MDAgaG92ZXI6Ym9yZGVyLVsjZmY2YjM1XSBob3ZlcjpiZy1bI2ZmNmIzNV0gdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgc3R5bGU9e3sgbGVmdDogLTggfX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMiBweC0yIHB5LTEgYmctZ3JheS04MDAvOTAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkIHRleHQteHMgdGV4dC1ncmF5LTMwMCBvcGFjaXR5LTAgaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgIHtpbnB1dExhYmVsfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBSb2xlIElucHV0IEhhbmRsZSB3aXRoIExhYmVsICovfVxuICAgICAge2hhc1JvbGVJbnB1dCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIHRvcC0xLzQgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICA8SGFuZGxlXG4gICAgICAgICAgICB0eXBlPVwidGFyZ2V0XCJcbiAgICAgICAgICAgIHBvc2l0aW9uPXtQb3NpdGlvbi5MZWZ0fVxuICAgICAgICAgICAgaWQ9XCJyb2xlXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgYm9yZGVyLTIgYm9yZGVyLXB1cnBsZS01MDAgYmctcHVycGxlLTcwMCBob3Zlcjpib3JkZXItcHVycGxlLTQwMCBob3ZlcjpiZy1wdXJwbGUtNDAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIHN0eWxlPXt7IGxlZnQ6IC02IH19XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTEgcHgtMiBweS0xIGJnLXB1cnBsZS05MDAvOTAgYm9yZGVyIGJvcmRlci1wdXJwbGUtNjAwIHJvdW5kZWQgdGV4dC14cyB0ZXh0LXB1cnBsZS0yMDAgb3BhY2l0eS0wIGhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICB7cm9sZUlucHV0TGFiZWx9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIE5vZGUgQm9keSAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPXtgbWluLXctWzIwMHB4XSByb3VuZGVkLWxnIGJvcmRlci0yIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgIGhhc0Vycm9yXG4gICAgICAgICAgICA/ICdib3JkZXItcmVkLTUwMCBiZy1yZWQtOTAwLzIwJ1xuICAgICAgICAgICAgOiBpc0NvbmZpZ3VyZWRcbiAgICAgICAgICAgID8gJ2JvcmRlci1ncmF5LTYwMCBiZy1ncmF5LTgwMC85MCdcbiAgICAgICAgICAgIDogJ2JvcmRlci15ZWxsb3ctNTAwIGJnLXllbGxvdy05MDAvMjAnXG4gICAgICAgIH0gYmFja2Ryb3AtYmx1ci1zbSBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsYH1cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICBib3JkZXJDb2xvcjogaGFzRXJyb3IgPyAnI2VmNDQ0NCcgOiBpc0NvbmZpZ3VyZWQgPyBjb2xvciA6ICcjZWFiMzA4J1xuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IFxuICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMyByb3VuZGVkLXQtbGcgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIlxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBoYXNFcnJvciBcbiAgICAgICAgICAgICAgPyAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyMzksIDY4LCA2OCwgMC4yKSwgcmdiYSgyMzksIDY4LCA2OCwgMC4xKSknXG4gICAgICAgICAgICAgIDogYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICR7Y29sb3J9MjAsICR7Y29sb3J9MTApYFxuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICB7SWNvbiAmJiAoXG4gICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBoYXNFcnJvciA/ICcjZWY0NDQ0MjAnIDogYCR7Y29sb3J9MjBgLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBoYXNFcnJvciA/ICcjZWY0NDQ0JyA6IGNvbG9yXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXdoaXRlIHRleHQtc21cIj5cbiAgICAgICAgICAgICAge2RhdGEubGFiZWx9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtkYXRhLmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgIHtkYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgey8qIFN0YXR1cyBJbmRpY2F0b3IgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAge2hhc0Vycm9yID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctcmVkLTUwMCByb3VuZGVkLWZ1bGxcIiB0aXRsZT1cIkVycm9yXCIgLz5cbiAgICAgICAgICAgICkgOiBpc0NvbmZpZ3VyZWQgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsXCIgdGl0bGU9XCJDb25maWd1cmVkXCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy15ZWxsb3ctNTAwIHJvdW5kZWQtZnVsbFwiIHRpdGxlPVwiTmVlZHMgY29uZmlndXJhdGlvblwiIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogSW5wdXQgSW5kaWNhdG9ycyAqL31cbiAgICAgICAgeyhoYXNJbnB1dCB8fCBoYXNSb2xlSW5wdXQpICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMCB0b3AtMCBoLWZ1bGwgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LWNlbnRlciBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICB7aGFzSW5wdXQgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTJcIiBzdHlsZT17eyBtYXJnaW5Ub3A6IGhhc1JvbGVJbnB1dCA/ICcyMCUnIDogJzAnIH19PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtNiBiZy1ncmF5LTUwMC81MCByb3VuZGVkLXJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTEgdGV4dC14cyB0ZXh0LWdyYXktNDAwIGJnLWdyYXktODAwLzgwIHB4LTEgcHktMC41IHJvdW5kZWQgdGV4dC1jZW50ZXIgbWluLXctWzQwcHhdXCI+XG4gICAgICAgICAgICAgICAgICBEYXRhXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtoYXNSb2xlSW5wdXQgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCIgc3R5bGU9e3sgbWFyZ2luVG9wOiBoYXNJbnB1dCA/ICctMTAlJyA6ICctMjUlJyB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTQgYmctcHVycGxlLTUwMC81MCByb3VuZGVkLXJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTEgdGV4dC14cyB0ZXh0LXB1cnBsZS0zMDAgYmctcHVycGxlLTkwMC84MCBweC0xIHB5LTAuNSByb3VuZGVkIHRleHQtY2VudGVyIG1pbi13LVs0MHB4XVwiPlxuICAgICAgICAgICAgICAgICAgUm9sZVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgIHtjaGlsZHJlbiAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTMgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwLzUwXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEVycm9yIE1lc3NhZ2UgKi99XG4gICAgICAgIHtoYXNFcnJvciAmJiBkYXRhLmVycm9yTWVzc2FnZSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctcmVkLTkwMC8zMCBib3JkZXItdCBib3JkZXItcmVkLTcwMC81MCByb3VuZGVkLWItbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXJlZC0zMDBcIj5cbiAgICAgICAgICAgICAge2RhdGEuZXJyb3JNZXNzYWdlfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE91dHB1dCBIYW5kbGUgKi99XG4gICAgICB7aGFzT3V0cHV0ICYmIChcbiAgICAgICAgPEhhbmRsZVxuICAgICAgICAgIHR5cGU9XCJzb3VyY2VcIlxuICAgICAgICAgIHBvc2l0aW9uPXtQb3NpdGlvbi5SaWdodH1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IGJvcmRlci0yIGJvcmRlci1ncmF5LTUwMCBiZy1ncmF5LTcwMCBob3Zlcjpib3JkZXItWyNmZjZiMzVdIGhvdmVyOmJnLVsjZmY2YjM1XSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgc3R5bGU9e3sgcmlnaHQ6IC04IH19XG4gICAgICAgIC8+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhhbmRsZSIsIlBvc2l0aW9uIiwiQmFzZU5vZGUiLCJkYXRhIiwiY2hpbGRyZW4iLCJpY29uIiwiSWNvbiIsImNvbG9yIiwiaGFzSW5wdXQiLCJoYXNPdXRwdXQiLCJoYXNSb2xlSW5wdXQiLCJpbnB1dExhYmVsIiwib3V0cHV0TGFiZWwiLCJyb2xlSW5wdXRMYWJlbCIsImNsYXNzTmFtZSIsImlzQ29uZmlndXJlZCIsImhhc0Vycm9yIiwiZGl2IiwidHlwZSIsInBvc2l0aW9uIiwiTGVmdCIsImlkIiwic3R5bGUiLCJsZWZ0IiwiYm9yZGVyQ29sb3IiLCJiYWNrZ3JvdW5kIiwiYmFja2dyb3VuZENvbG9yIiwibGFiZWwiLCJkZXNjcmlwdGlvbiIsInRpdGxlIiwibWFyZ2luVG9wIiwiZXJyb3JNZXNzYWdlIiwiUmlnaHQiLCJyaWdodCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\n"));

/***/ })

});