"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/VisionNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisionNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction VisionNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#8b5cf6'; // Purple default for vision\n    const providerName = providerId ? providerNames[providerId] : 'Vision AI';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: typeof color === 'string' ? color : '#8b5cf6',\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"Vision\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 43,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"Vision AI Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to multimodal AI models for image analysis and vision tasks.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 77,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c = VisionNode;\nvar _c;\n$RefreshReg$(_c, \"VisionNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx\n"));

/***/ })

});