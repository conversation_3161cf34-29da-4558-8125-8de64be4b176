"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/BaseNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BaseNode(param) {\n    let { data, children, icon: Icon, color = '#ff6b35', hasInput = true, hasOutput = true, hasRoleInput = false, inputLabel = 'Input', outputLabel = 'Output', roleInputLabel = 'Role', className = '' } = param;\n    const isConfigured = data.isConfigured;\n    const hasError = data.hasError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-1/2 transform -translate-y-1/2 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"input\",\n                        className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                        style: {\n                            left: -8\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-2 px-2 py-1 bg-gray-800/90 border border-gray-600 rounded text-xs text-gray-300 opacity-0 hover:opacity-100 transition-opacity pointer-events-none\",\n                        children: inputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this),\n            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-0 top-1/4 transform -translate-y-1/2 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                        type: \"target\",\n                        position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                        id: \"role\",\n                        className: \"w-3 h-3 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors\",\n                        style: {\n                            left: -6\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-1 px-2 py-1 bg-purple-900/90 border border-purple-600 rounded text-xs text-purple-200 opacity-0 hover:opacity-100 transition-opacity pointer-events-none\",\n                        children: roleInputLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 transition-all duration-200 \".concat(hasError ? 'border-red-500 bg-red-900/20' : isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : \"linear-gradient(135deg, \".concat(color, \"20, \").concat(color, \"10)\")\n                        },\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: hasError ? '#ef444420' : \"\".concat(color, \"20\"),\n                                    color: hasError ? '#ef4444' : color\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    data.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: data.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\",\n                                    title: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this) : isConfigured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                    title: \"Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\",\n                                    title: \"Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    (hasInput || hasRoleInput) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -left-12 top-0 h-full flex flex-col justify-center space-y-2 pointer-events-none\",\n                        children: [\n                            hasRoleInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-300 bg-purple-900/90 border border-purple-700/50 px-2 py-1 rounded-lg shadow-lg\",\n                                        children: \"Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-0.5 bg-purple-500/50 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this),\n                            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-300 bg-gray-800/90 border border-gray-600/50 px-2 py-1 rounded-lg shadow-lg\",\n                                        children: \"Data\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-0.5 bg-gray-500/50 ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    hasError && data.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-red-300\",\n                            children: data.errorMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            hasOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    right: -8\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\n"));

/***/ })

});