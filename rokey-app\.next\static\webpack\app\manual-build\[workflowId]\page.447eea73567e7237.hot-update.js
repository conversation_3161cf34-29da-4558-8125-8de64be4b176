"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/ProviderNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction ProviderNode(param) {\n    let { data, id } = param;\n    _s();\n    const { getEdges, getNodes } = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useReactFlow)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#ff6b35';\n    const providerName = providerId ? providerNames[providerId] : 'AI Provider';\n    // Get connected role nodes\n    const connectedRoles = getEdges().filter((edge)=>edge.target === id && edge.targetHandle === 'role').map((edge)=>{\n        // Find the source node to get role information\n        const sourceNode = getNodes().find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'roleAgent') {\n            const roleConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleName) || 'Unknown Role',\n                type: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) || 'predefined'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: typeof color === 'string' ? color : '#ff6b35',\n        hasInput: true,\n        hasOutput: true,\n        hasRoleInput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            providerId === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"300+ Models\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 63,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"AI Provider Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to OpenAI, Anthropic, Google, DeepSeek, xAI, or OpenRouter.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 99,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(ProviderNode, \"RZ7Xaupw8bsRjC9QeKwHIIgcTfo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useReactFlow\n    ];\n});\n_c = ProviderNode;\nvar _c;\n$RefreshReg$(_c, \"ProviderNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx\n"));

/***/ })

});