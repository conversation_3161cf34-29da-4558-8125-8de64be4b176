"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/VisionNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisionNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction VisionNode(param) {\n    let { data, id } = param;\n    _s();\n    const { getEdges, getNodes } = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useReactFlow)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#8b5cf6'; // Purple default for vision\n    const providerName = providerId ? providerNames[providerId] : 'Vision AI';\n    // Get connected role nodes\n    const connectedRoles = getEdges().filter((edge)=>edge.target === id && edge.targetHandle === 'role').map((edge)=>{\n        // Find the source node to get role information\n        const sourceNode = getNodes().find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'roleAgent') {\n            const roleConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleName) || 'Unknown Role',\n                type: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) || 'predefined'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: typeof color === 'string' ? color : '#8b5cf6',\n        hasInput: true,\n        hasOutput: true,\n        hasRoleInput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"Vision\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 63,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"Vision AI Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to multimodal AI models for image analysis and vision tasks.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 97,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(VisionNode, \"RZ7Xaupw8bsRjC9QeKwHIIgcTfo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useReactFlow\n    ];\n});\n_c = VisionNode;\nvar _c;\n$RefreshReg$(_c, \"VisionNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx\n"));

/***/ })

});