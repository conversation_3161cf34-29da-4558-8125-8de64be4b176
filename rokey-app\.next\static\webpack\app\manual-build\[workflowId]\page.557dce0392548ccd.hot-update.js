"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction EyeIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    }));\n}\n_c = EyeIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"EyeIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/manual-build/nodes/VisionNode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisionNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction VisionNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#8b5cf6'; // Purple default for vision\n    const providerName = providerId ? providerNames[providerId] : 'Vision AI';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: typeof color === 'string' ? color : '#8b5cf6',\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"Vision\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 43,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"Vision AI Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to multimodal AI models for image analysis and vision tasks.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n                lineNumber: 77,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\VisionNode.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c = VisionNode;\nvar _c;\n$RefreshReg$(_c, \"VisionNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/index.ts":
/*!****************************************************!*\
  !*** ./src/components/manual-build/nodes/index.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClassifierNode: () => (/* reexport safe */ _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ConditionalNode: () => (/* reexport safe */ _ConditionalNode__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   LoopNode: () => (/* reexport safe */ _LoopNode__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   MemoryNode: () => (/* reexport safe */ _MemoryNode__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   MergeNode: () => (/* reexport safe */ _MergeNode__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   OutputNode: () => (/* reexport safe */ _OutputNode__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   ProviderNode: () => (/* reexport safe */ _ProviderNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   RoleAgentNode: () => (/* reexport safe */ _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   SwitchNode: () => (/* reexport safe */ _SwitchNode__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   ToolNode: () => (/* reexport safe */ _ToolNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   UserRequestNode: () => (/* reexport safe */ _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   nodeTypes: () => (/* binding */ nodeTypes)\n/* harmony export */ });\n/* harmony import */ var _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UserRequestNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/UserRequestNode.tsx\");\n/* harmony import */ var _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ClassifierNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ClassifierNode.tsx\");\n/* harmony import */ var _ProviderNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProviderNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx\");\n/* harmony import */ var _VisionNode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VisionNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/VisionNode.tsx\");\n/* harmony import */ var _OutputNode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./OutputNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/OutputNode.tsx\");\n/* harmony import */ var _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RoleAgentNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/RoleAgentNode.tsx\");\n/* harmony import */ var _ConditionalNode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ConditionalNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ConditionalNode.tsx\");\n/* harmony import */ var _MergeNode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MergeNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/MergeNode.tsx\");\n/* harmony import */ var _LoopNode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LoopNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/LoopNode.tsx\");\n/* harmony import */ var _ToolNode__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ToolNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\");\n/* harmony import */ var _MemoryNode__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./MemoryNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/MemoryNode.tsx\");\n/* harmony import */ var _SwitchNode__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SwitchNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/SwitchNode.tsx\");\n// Node Types Registry for React Flow\n// This file exports all custom node components for the Manual Build workflow editor\n\n\n\n\n\n\n\n\n\n\n\n\n// Export all node types for React Flow\nconst nodeTypes = {\n    userRequest: _UserRequestNode__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    classifier: _ClassifierNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    provider: _ProviderNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    vision: _VisionNode__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    output: _OutputNode__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    roleAgent: _RoleAgentNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    conditional: _ConditionalNode__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    merge: _MergeNode__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    loop: _LoopNode__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    tool: _ToolNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    memory: _MemoryNode__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    switch: _SwitchNode__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n};\n// Export individual components\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/index.ts\n"));

/***/ })

});