'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { XMarkIcon, Cog6ToothIcon, CloudArrowDownIcon } from '@heroicons/react/24/outline';
import { WorkflowNode, ProviderNodeData, VisionNodeData, RoleAgentNodeData } from '@/types/manualBuild';
import { llmProviders } from '@/config/models';
import { PREDEFINED_ROLES, type Role } from '@/config/roles';

interface NodeConfigPanelProps {
  node: WorkflowNode;
  onUpdate: (updates: Partial<WorkflowNode['data']>) => void;
  onClose: () => void;
}

const PROVIDER_OPTIONS = llmProviders.map(p => ({ value: p.id, label: p.name }));

interface ModelInfo {
  id: string;
  name: string;
  display_name?: string;
  provider_id: string;
  modality?: string;
  context_window?: number;
  input_token_limit?: number;
  output_token_limit?: number;
}

export default function NodeConfigPanel({ node, onUpdate, onClose }: NodeConfigPanelProps) {
  const [config, setConfig] = useState(node.data.config);
  const [fetchedProviderModels, setFetchedProviderModels] = useState<ModelInfo[] | null>(null);
  const [isFetchingProviderModels, setIsFetchingProviderModels] = useState(false);
  const [fetchProviderModelsError, setFetchProviderModelsError] = useState<string | null>(null);

  // Role management state
  const [customRoles, setCustomRoles] = useState<Array<{
    id: string;
    role_id: string;
    name: string;
    description?: string;
    user_id: string;
    created_at: string;
    updated_at: string;
  }>>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [rolesError, setRolesError] = useState<string | null>(null);

  // Fetch models from database
  const fetchModelsFromDatabase = useCallback(async () => {
    setIsFetchingProviderModels(true);
    setFetchProviderModelsError(null);
    setFetchedProviderModels(null);
    try {
      const response = await fetch('/api/providers/list-models', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch models from database.');
      }
      if (data.models) {
        setFetchedProviderModels(data.models);
      } else {
        setFetchedProviderModels([]);
      }
    } catch (err: any) {
      console.error('Error fetching models:', err);
      setFetchProviderModelsError(err.message);
      setFetchedProviderModels([]);
    } finally {
      setIsFetchingProviderModels(false);
    }
  }, []);

  // Fetch custom roles from database
  const fetchCustomRoles = useCallback(async () => {
    setIsLoadingRoles(true);
    setRolesError(null);

    try {
      const response = await fetch('/api/user/custom-roles');
      if (!response.ok) {
        throw new Error('Failed to fetch custom roles');
      }
      const roles = await response.json();
      setCustomRoles(roles);
    } catch (err: any) {
      console.error('Error fetching custom roles:', err);
      setRolesError(err.message);
      setCustomRoles([]);
    } finally {
      setIsLoadingRoles(false);
    }
  }, []);

  // Load models and roles on component mount
  useEffect(() => {
    if (node.type === 'provider' || node.type === 'vision') {
      fetchModelsFromDatabase();
    }
    if (node.type === 'roleAgent') {
      fetchCustomRoles();
    }
  }, [node.type, fetchModelsFromDatabase, fetchCustomRoles]);

  // Auto-select first model when provider changes or models load
  useEffect(() => {
    if ((node.type === 'provider' || node.type === 'vision') && fetchedProviderModels && fetchedProviderModels.length > 0) {
      const providerConfig = config as ProviderNodeData['config'] | VisionNodeData['config'];
      const currentProviderDetails = llmProviders.find(p => p.id === providerConfig.providerId);

      if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {
        let availableModels: { value: string; label: string; provider_id?: string; }[] = [];

        if (currentProviderDetails.id === "openrouter") {
          availableModels = fetchedProviderModels
            .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
            .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
        } else if (currentProviderDetails.id === "deepseek") {
          const deepseekChatModel = fetchedProviderModels.find(
            (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
          );
          if (deepseekChatModel) {
            availableModels.push({
              value: "deepseek-chat",
              label: "Deepseek V3",
              provider_id: "deepseek",
            });
          }
          const deepseekReasonerModel = fetchedProviderModels.find(
            (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
          );
          if (deepseekReasonerModel) {
            availableModels.push({
              value: "deepseek-reasoner",
              label: "DeepSeek R1-0528",
              provider_id: "deepseek",
            });
          }
        } else {
          availableModels = fetchedProviderModels
            .filter(model => model.provider_id === currentProviderDetails.id)
            .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
            .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
        }

        if (availableModels.length > 0) {
          const selectedModelId = availableModels[0].value;
          const selectedModel = fetchedProviderModels.find(m => m.id === selectedModelId);

          // Set reasonable default for maxTokens based on model limits
          const defaultMaxTokens = selectedModel?.output_token_limit || selectedModel?.context_window || 4096;
          const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));

          const currentParams = providerConfig.parameters || {};

          // Update config in a single call to avoid infinite loops
          const newConfig = {
            ...providerConfig,
            modelId: selectedModelId,
            parameters: {
              ...currentParams,
              maxTokens: currentParams.maxTokens || reasonableDefault
            }
          };

          setConfig(newConfig);
          onUpdate({
            config: newConfig,
            isConfigured: isNodeConfigured(node.type, newConfig)
          });
        }
      }
    }
  }, [fetchedProviderModels, node.type, (config as ProviderNodeData['config'])?.providerId]); // Only re-run when provider changes

  const handleConfigChange = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    onUpdate({
      config: newConfig,
      isConfigured: isNodeConfigured(node.type, newConfig)
    });
  };

  const handleProviderConfigChange = (key: string, value: any) => {
    const currentConfig = config as ProviderNodeData['config'];
    const newConfig = {
      ...currentConfig,
      [key]: value
    };

    // Only initialize parameters if they don't exist and we're setting a parameter
    if (key === 'parameters' || !currentConfig.parameters) {
      newConfig.parameters = {
        temperature: 1.0,
        maxTokens: undefined,
        topP: undefined,
        frequencyPenalty: undefined,
        presencePenalty: undefined,
        ...currentConfig.parameters,
        ...(key === 'parameters' ? value : {})
      };
    }

    setConfig(newConfig);
    onUpdate({
      config: newConfig,
      isConfigured: isNodeConfigured(node.type, newConfig)
    });
  };

  // Model options based on selected provider and fetched models
  const modelOptions = useMemo(() => {
    if (fetchedProviderModels && (node.type === 'provider' || node.type === 'vision')) {
      const providerConfig = config as ProviderNodeData['config'] | VisionNodeData['config'];

      const currentProviderDetails = llmProviders.find(p => p.id === providerConfig.providerId);
      if (!currentProviderDetails) {
        return [];
      }

      // Filter function for vision nodes - only show multimodal models
      const filterForVision = (models: any[]) => {
        if (node.type === 'vision') {
          return models.filter(model =>
            model.modality &&
            (model.modality.includes('multimodal') ||
             model.modality.includes('vision') ||
             model.modality.includes('image'))
          );
        }
        return models;
      };

      // If the selected provider is "OpenRouter", show all fetched models (filtered for vision if needed)
      if (currentProviderDetails.id === "openrouter") {
        const filteredModels = filterForVision(fetchedProviderModels);
        return filteredModels
          .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
          .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // Custom logic for DeepSeek
      if (currentProviderDetails.id === "deepseek") {
        const deepseekOptions: { value: string; label: string; provider_id?: string; }[] = [];
        const deepseekChatModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
        );
        if (deepseekChatModel && (node.type === 'provider' || (node.type === 'vision' && deepseekChatModel.modality?.includes('multimodal')))) {
          deepseekOptions.push({
            value: "deepseek-chat",
            label: "Deepseek V3",
            provider_id: "deepseek",
          });
        }
        const deepseekReasonerModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
        );
        if (deepseekReasonerModel && (node.type === 'provider' || (node.type === 'vision' && deepseekReasonerModel.modality?.includes('multimodal')))) {
          deepseekOptions.push({
            value: "deepseek-reasoner",
            label: "DeepSeek R1-0528",
            provider_id: "deepseek",
          });
        }
        return deepseekOptions.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // For other providers, filter by their specific provider_id and vision capabilities
      const providerModels = fetchedProviderModels.filter(model => model.provider_id === currentProviderDetails.id);
      const filteredModels = filterForVision(providerModels);
      return filteredModels
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
        .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }
    return [];
  }, [fetchedProviderModels, config, node.type]);

  // Get current model's token limits
  const getCurrentModelLimits = useMemo(() => {
    if (!fetchedProviderModels || (node.type !== 'provider' && node.type !== 'vision')) {
      return { maxTokens: 4096, minTokens: 1 }; // Default fallback
    }

    const providerConfig = config as ProviderNodeData['config'] | VisionNodeData['config'];
    if (!providerConfig?.modelId) {
      return { maxTokens: 4096, minTokens: 1 }; // Default when no model selected
    }

    const currentModel = fetchedProviderModels.find(m => m.id === providerConfig.modelId);
    if (!currentModel) {
      return { maxTokens: 4096, minTokens: 1 }; // Default when model not found
    }

    // Use output_token_limit if available, otherwise context_window, otherwise default
    const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;
    const minTokens = 1;

    return { maxTokens, minTokens };
  }, [fetchedProviderModels, config, node.type]);

  const isNodeConfigured = (nodeType: string, nodeConfig: any): boolean => {
    switch (nodeType) {
      case 'provider':
        return !!(nodeConfig.providerId && nodeConfig.modelId);
      case 'vision':
        return !!(nodeConfig.providerId && nodeConfig.modelId);
      case 'roleAgent':
        if (nodeConfig.roleType === 'new') {
          return !!(nodeConfig.newRoleName && nodeConfig.customPrompt);
        }
        return !!(nodeConfig.roleId && nodeConfig.roleName);
      case 'conditional':
        return !!(nodeConfig.condition && nodeConfig.conditionType);
      case 'tool':
        return !!(nodeConfig.toolType);
      case 'memory':
        return !!(nodeConfig.memoryType && nodeConfig.storageKey);
      case 'switch':
        return !!(nodeConfig.switchType && nodeConfig.cases?.length > 0);
      case 'loop':
        return !!(nodeConfig.loopType);
      default:
        return true;
    }
  };

  const renderProviderConfig = () => {
    const providerConfig = config as ProviderNodeData['config'];

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Provider
          </label>
          <select
            value={providerConfig?.providerId || ''}
            onChange={(e) => {
              const currentConfig = config as ProviderNodeData['config'];
              const newConfig = {
                ...currentConfig,
                providerId: e.target.value as any,
                modelId: '', // Reset model when provider changes
                parameters: currentConfig.parameters || {
                  temperature: 1.0,
                  maxTokens: undefined,
                  topP: undefined,
                  frequencyPenalty: undefined,
                  presencePenalty: undefined,
                }
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          >
            <option value="">Select Provider</option>
            {PROVIDER_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            API Key
          </label>
          <input
            type="password"
            value={providerConfig?.apiKey || ''}
            onChange={(e) => handleProviderConfigChange('apiKey', e.target.value)}
            placeholder="Enter your API key"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"
          />
          {isFetchingProviderModels && fetchedProviderModels === null && (
            <p className="mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg">
              <CloudArrowDownIcon className="h-4 w-4 mr-1 animate-pulse" />
              Fetching models...
            </p>
          )}
          {fetchProviderModelsError && (
            <p className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg">
              Error: {fetchProviderModelsError}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Model Variant
          </label>
          <select
            value={providerConfig?.modelId || ''}
            onChange={(e) => {
              const selectedModelId = e.target.value;

              // Update maxTokens based on the selected model
              let updatedConfig = { ...providerConfig, modelId: selectedModelId };

              if (selectedModelId && fetchedProviderModels) {
                const selectedModel = fetchedProviderModels.find(m => m.id === selectedModelId);
                if (selectedModel) {
                  const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;
                  const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));

                  const currentParams = providerConfig?.parameters || {};
                  updatedConfig = {
                    ...updatedConfig,
                    parameters: {
                      ...currentParams,
                      maxTokens: reasonableDefault
                    }
                  };
                }
              }

              // Single state update to avoid infinite loops
              setConfig(updatedConfig);
              onUpdate({
                config: updatedConfig,
                isConfigured: isNodeConfigured(node.type, updatedConfig)
              });
            }}
            disabled={!providerConfig?.providerId || !modelOptions.length}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30"
          >
            {!providerConfig?.providerId ? (
              <option value="" disabled>Select a provider first</option>
            ) : modelOptions.length > 0 ? (
              <>
                <option value="">Select Model</option>
                {modelOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </>
            ) : (
              <option value="" disabled>
                {isFetchingProviderModels ? 'Loading models...' : 'No models available'}
              </option>
            )}
          </select>
        </div>

        <div>
          <label htmlFor="temperature" className="block text-sm font-medium text-gray-300 mb-2">
            Temperature
            <span className="text-xs text-gray-400 ml-1">(0.0 - 2.0)</span>
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="temperature"
              min="0"
              max="2"
              step="0.1"
              value={providerConfig?.parameters?.temperature || 1.0}
              onChange={(e) => {
                const temp = parseFloat(e.target.value);
                const currentParams = providerConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  temperature: temp
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Conservative</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={providerConfig?.parameters?.temperature || 1.0}
                  onChange={(e) => {
                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));
                    const currentParams = providerConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      temperature: temp
                    });
                  }}
                  className="w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
              </div>
              <span className="text-xs text-gray-400">Creative</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative
            </p>
          </div>
        </div>

        <div>
          <label htmlFor="maxTokens" className="block text-sm font-medium text-gray-300 mb-2">
            Max Tokens
            <span className="text-xs text-gray-400 ml-1">
              ({getCurrentModelLimits.minTokens} - {getCurrentModelLimits.maxTokens.toLocaleString()})
            </span>
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="maxTokens"
              min={getCurrentModelLimits.minTokens}
              max={getCurrentModelLimits.maxTokens}
              step="1"
              value={providerConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
              onChange={(e) => {
                const value = parseInt(e.target.value);
                const currentParams = providerConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  maxTokens: value
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Minimal</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min={getCurrentModelLimits.minTokens}
                  max={getCurrentModelLimits.maxTokens}
                  step="1"
                  value={providerConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
                  onChange={(e) => {
                    const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));
                    const currentParams = providerConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: value
                    });
                  }}
                  className="w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
                <button
                  type="button"
                  onClick={() => {
                    const currentParams = providerConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: getCurrentModelLimits.maxTokens
                    });
                  }}
                  className="text-xs text-orange-400 hover:text-orange-300 underline"
                >
                  Max
                </button>
              </div>
              <span className="text-xs text-gray-400">Maximum</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.
            </p>
          </div>
        </div>

        {providerConfig?.providerId === 'openrouter' && (
          <div className="p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg">
            <div className="text-sm text-blue-300 font-medium mb-1">🌐 OpenRouter</div>
            <div className="text-xs text-blue-200">
              Access to 300+ models from multiple providers with a single API key.
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderVisionConfig = () => {
    const visionConfig = config as VisionNodeData['config'];

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Provider
          </label>
          <select
            value={visionConfig?.providerId || ''}
            onChange={(e) => {
              const currentConfig = config as VisionNodeData['config'];
              const newConfig = {
                ...currentConfig,
                providerId: e.target.value as any,
                modelId: '', // Reset model when provider changes
                parameters: currentConfig.parameters || {
                  temperature: 1.0,
                  maxTokens: undefined,
                  topP: undefined,
                  frequencyPenalty: undefined,
                  presencePenalty: undefined,
                }
              };
              setConfig(newConfig);
              onUpdate({
                config: newConfig,
                isConfigured: isNodeConfigured(node.type, newConfig)
              });
            }}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          >
            <option value="">Select Provider</option>
            {PROVIDER_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            API Key
          </label>
          <input
            type="password"
            value={visionConfig?.apiKey || ''}
            onChange={(e) => handleProviderConfigChange('apiKey', e.target.value)}
            placeholder="Enter your API key"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"
          />
          {isFetchingProviderModels && fetchedProviderModels === null && (
            <p className="mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg">
              <CloudArrowDownIcon className="w-4 h-4 mr-2" />
              Fetching models...
            </p>
          )}
          {fetchProviderModelsError && (
            <p className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg">
              Error: {fetchProviderModelsError}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Vision Model
            <span className="text-xs text-purple-400 ml-1">(Multimodal Only)</span>
          </label>
          <select
            value={visionConfig?.modelId || ''}
            onChange={(e) => {
              const selectedModelId = e.target.value;

              // Update maxTokens based on the selected model
              let updatedConfig = { ...visionConfig, modelId: selectedModelId };

              if (selectedModelId && fetchedProviderModels) {
                const selectedModel = fetchedProviderModels.find(m => m.id === selectedModelId);
                if (selectedModel) {
                  const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;
                  const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));

                  const currentParams = visionConfig?.parameters || {};
                  updatedConfig = {
                    ...updatedConfig,
                    parameters: {
                      ...currentParams,
                      maxTokens: reasonableDefault
                    }
                  };
                }
              }

              // Single state update to avoid infinite loops
              setConfig(updatedConfig);
              onUpdate({
                config: updatedConfig,
                isConfigured: isNodeConfigured(node.type, updatedConfig)
              });
            }}
            disabled={!visionConfig?.providerId || !modelOptions.length}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30"
          >
            {!visionConfig?.providerId ? (
              <option value="" disabled>Select a provider first</option>
            ) : modelOptions.length > 0 ? (
              <>
                <option value="">Select Vision Model</option>
                {modelOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </>
            ) : (
              <option value="" disabled>
                {isFetchingProviderModels ? 'Loading models...' : 'No vision models available'}
              </option>
            )}
          </select>
          {modelOptions.length === 0 && visionConfig?.providerId && !isFetchingProviderModels && (
            <p className="mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg">
              ⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities.
            </p>
          )}
        </div>

        {/* Temperature and Max Tokens controls - same as Provider node */}
        <div>
          <label htmlFor="temperature" className="block text-sm font-medium text-gray-300 mb-2">
            Temperature (0.0 - 2.0)
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="temperature"
              min="0"
              max="2"
              step="0.1"
              value={visionConfig?.parameters?.temperature || 1.0}
              onChange={(e) => {
                const temp = parseFloat(e.target.value);
                const currentParams = visionConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  temperature: temp
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Conservative</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={visionConfig?.parameters?.temperature || 1.0}
                  onChange={(e) => {
                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));
                    const currentParams = visionConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      temperature: temp
                    });
                  }}
                  className="w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
              </div>
              <span className="text-xs text-gray-400">Creative</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative
            </p>
          </div>
        </div>

        <div>
          <label htmlFor="maxTokens" className="block text-sm font-medium text-gray-300 mb-2">
            Max Tokens
            <span className="text-xs text-gray-400 ml-1">
              ({getCurrentModelLimits.minTokens} - {getCurrentModelLimits.maxTokens.toLocaleString()})
            </span>
          </label>
          <div className="space-y-2">
            <input
              type="range"
              id="maxTokens"
              min={getCurrentModelLimits.minTokens}
              max={getCurrentModelLimits.maxTokens}
              step="1"
              value={visionConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
              onChange={(e) => {
                const value = parseInt(e.target.value);
                const currentParams = visionConfig?.parameters || {};
                handleProviderConfigChange('parameters', {
                  ...currentParams,
                  maxTokens: value
                });
              }}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"
            />
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-400">Minimal</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min={getCurrentModelLimits.minTokens}
                  max={getCurrentModelLimits.maxTokens}
                  step="1"
                  value={visionConfig?.parameters?.maxTokens || getCurrentModelLimits.maxTokens}
                  onChange={(e) => {
                    const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));
                    const currentParams = visionConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: value
                    });
                  }}
                  className="w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"
                />
                <button
                  type="button"
                  onClick={() => {
                    const currentParams = visionConfig?.parameters || {};
                    handleProviderConfigChange('parameters', {
                      ...currentParams,
                      maxTokens: getCurrentModelLimits.maxTokens
                    });
                  }}
                  className="text-xs text-orange-400 hover:text-orange-300 underline"
                >
                  Max
                </button>
              </div>
              <span className="text-xs text-gray-400">Maximum</span>
            </div>
            <p className="text-xs text-gray-400">
              Controls the maximum number of tokens the model can generate for vision analysis.
            </p>
          </div>
        </div>

        {visionConfig?.providerId === 'openrouter' && (
          <div className="p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg">
            <div className="text-sm text-purple-300 font-medium mb-1">👁️ Vision Models</div>
            <div className="text-xs text-purple-200">
              Access to multimodal models from multiple providers for image analysis and vision tasks.
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderRoleAgentConfig = () => {
    const roleConfig = config as RoleAgentNodeData['config'];

    // Combine predefined and custom roles for dropdown
    const availableRoles = [
      ...PREDEFINED_ROLES.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        type: 'predefined' as const
      })),
      ...customRoles.map(role => ({
        id: role.role_id,
        name: role.name,
        description: role.description,
        type: 'custom' as const
      }))
    ];

    const handleRoleSelectionChange = (value: string) => {
      if (value === 'create_new') {
        // Switch to create new role mode
        const newConfig = {
          ...roleConfig,
          roleType: 'new' as const,
          roleId: '',
          roleName: '',
          newRoleName: '',
          newRoleDescription: '',
          customPrompt: ''
        };
        setConfig(newConfig);
        onUpdate({
          config: newConfig,
          isConfigured: isNodeConfigured(node.type, newConfig)
        });
      } else {
        // Select existing role
        const selectedRole = availableRoles.find(role => role.id === value);
        if (selectedRole) {
          const newConfig = {
            ...roleConfig,
            roleType: selectedRole.type,
            roleId: selectedRole.id,
            roleName: selectedRole.name,
            customPrompt: selectedRole.description || ''
          };
          setConfig(newConfig);
          onUpdate({
            config: newConfig,
            isConfigured: isNodeConfigured(node.type, newConfig)
          });
        }
      }
    };

    const handleNewRoleChange = (field: string, value: string) => {
      const newConfig = {
        ...roleConfig,
        [field]: value
      };
      setConfig(newConfig);
      onUpdate({
        config: newConfig,
        isConfigured: isNodeConfigured(node.type, newConfig)
      });
    };

    return (
      <div className="space-y-4">
        {/* Role Selection Dropdown */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Select Role
          </label>
          {isLoadingRoles ? (
            <div className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400">
              Loading roles...
            </div>
          ) : (
            <select
              value={roleConfig?.roleType === 'new' ? 'create_new' : roleConfig?.roleId || ''}
              onChange={(e) => handleRoleSelectionChange(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
            >
              <option value="">Select a role...</option>

              {/* Predefined Roles */}
              <optgroup label="System Roles">
                {PREDEFINED_ROLES.map(role => (
                  <option key={role.id} value={role.id}>
                    {role.name}
                  </option>
                ))}
              </optgroup>

              {/* Custom Roles */}
              {customRoles.length > 0 && (
                <optgroup label="Your Custom Roles">
                  {customRoles.map(role => (
                    <option key={role.role_id} value={role.role_id}>
                      {role.name}
                    </option>
                  ))}
                </optgroup>
              )}

              {/* Create New Option */}
              <optgroup label="Create New">
                <option value="create_new">+ Create New Role</option>
              </optgroup>
            </select>
          )}

          {rolesError && (
            <p className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg">
              Error loading roles: {rolesError}
            </p>
          )}
        </div>

        {/* Show role description for selected role */}
        {roleConfig?.roleType !== 'new' && roleConfig?.roleId && (
          <div className="p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg">
            <div className="text-sm font-medium text-white mb-1">
              {roleConfig.roleName}
            </div>
            {roleConfig.customPrompt && (
              <div className="text-xs text-gray-300">
                {roleConfig.customPrompt}
              </div>
            )}
          </div>
        )}

        {/* Create New Role Fields */}
        {roleConfig?.roleType === 'new' && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                New Role Name
              </label>
              <input
                type="text"
                value={roleConfig.newRoleName || ''}
                onChange={(e) => handleNewRoleChange('newRoleName', e.target.value)}
                placeholder="e.g., Data Analyst, Creative Writer"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Role Description
              </label>
              <input
                type="text"
                value={roleConfig.newRoleDescription || ''}
                onChange={(e) => handleNewRoleChange('newRoleDescription', e.target.value)}
                placeholder="Brief description of this role's purpose"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Custom Instructions
              </label>
              <textarea
                value={roleConfig.customPrompt || ''}
                onChange={(e) => handleNewRoleChange('customPrompt', e.target.value)}
                placeholder="Enter detailed instructions for this role..."
                rows={4}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
              />
            </div>
          </>
        )}

        {/* Memory Toggle */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={roleConfig?.memoryEnabled || false}
              onChange={(e) => handleConfigChange('memoryEnabled', e.target.checked)}
              className="rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"
            />
            <span className="ml-2 text-sm text-gray-300">Enable memory</span>
          </label>
          <p className="text-xs text-gray-400 mt-1 ml-6">
            Allow this role to remember context from previous interactions
          </p>
        </div>
      </div>
    );
  };

  const renderConditionalConfig = () => {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Condition Type
          </label>
          <select
            value={config.conditionType || ''}
            onChange={(e) => handleConfigChange('conditionType', e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          >
            <option value="">Select Type</option>
            <option value="contains">Contains</option>
            <option value="equals">Equals</option>
            <option value="regex">Regex</option>
            <option value="length">Length</option>
            <option value="custom">Custom</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Condition
          </label>
          <input
            type="text"
            value={config.condition || ''}
            onChange={(e) => handleConfigChange('condition', e.target.value)}
            placeholder="Enter condition..."
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              True Label
            </label>
            <input
              type="text"
              value={config.trueLabel || ''}
              onChange={(e) => handleConfigChange('trueLabel', e.target.value)}
              placeholder="Continue"
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              False Label
            </label>
            <input
              type="text"
              value={config.falseLabel || ''}
              onChange={(e) => handleConfigChange('falseLabel', e.target.value)}
              placeholder="Skip"
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
            />
          </div>
        </div>
      </div>
    );
  };

  const renderDefaultConfig = () => {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Label
          </label>
          <input
            type="text"
            value={node.data.label}
            onChange={(e) => onUpdate({ label: e.target.value })}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Description
          </label>
          <textarea
            value={node.data.description || ''}
            onChange={(e) => onUpdate({ description: e.target.value })}
            rows={3}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"
          />
        </div>
      </div>
    );
  };

  const renderConfigContent = () => {
    switch (node.type) {
      case 'provider':
        return renderProviderConfig();
      case 'vision':
        return renderVisionConfig();
      case 'roleAgent':
        return renderRoleAgentConfig();
      case 'conditional':
        return renderConditionalConfig();
      default:
        return renderDefaultConfig();
    }
  };

  return (
    <div className="w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
            <Cog6ToothIcon className="w-5 h-5 text-[#ff6b35]" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              Configure Node
            </h3>
            <p className="text-sm text-gray-400">
              {node.data.label}
            </p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors p-1 rounded"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>

      {/* Configuration Form */}
      <div className="space-y-6">
        {renderConfigContent()}
      </div>

      {/* Status */}
      <div className="mt-6 p-3 rounded-lg border border-gray-700/50">
        <div className="flex items-center gap-2 mb-2">
          <div className={`w-2 h-2 rounded-full ${
            node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500'
          }`} />
          <span className="text-sm font-medium text-white">
            {node.data.isConfigured ? 'Configured' : 'Needs Configuration'}
          </span>
        </div>
        <p className="text-xs text-gray-400">
          {node.data.isConfigured 
            ? 'This node is properly configured and ready to use.'
            : 'Complete the configuration to use this node in your workflow.'
          }
        </p>
      </div>
    </div>
  );
}
