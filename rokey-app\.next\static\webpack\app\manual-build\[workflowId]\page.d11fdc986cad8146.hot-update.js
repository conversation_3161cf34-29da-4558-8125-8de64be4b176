"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Local state for max tokens slider to avoid conflicts\n    const [localMaxTokens, setLocalMaxTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Load models on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider') {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase\n    ]);\n    // Auto-select first model when provider changes or models load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' && fetchedProviderModels) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useEffect.currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useEffect.currentProviderDetails\"]);\n                if (currentProviderDetails && !providerConfig.modelId) {\n                    let availableModels = [];\n                    if (currentProviderDetails.id === \"openrouter\") {\n                        availableModels = fetchedProviderModels.map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    } else if (currentProviderDetails.id === \"deepseek\") {\n                        const deepseekChatModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekChatModel\"]);\n                        if (deepseekChatModel) {\n                            availableModels.push({\n                                value: \"deepseek-chat\",\n                                label: \"Deepseek V3\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                        const deepseekReasonerModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekReasonerModel\"]);\n                        if (deepseekReasonerModel) {\n                            availableModels.push({\n                                value: \"deepseek-reasoner\",\n                                label: \"DeepSeek R1-0528\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                    } else {\n                        availableModels = fetchedProviderModels.filter({\n                            \"NodeConfigPanel.useEffect\": (model)=>model.provider_id === currentProviderDetails.id\n                        }[\"NodeConfigPanel.useEffect\"]).map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    }\n                    if (availableModels.length > 0) {\n                        const selectedModelId = availableModels[0].value;\n                        const selectedModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.selectedModel\": (m)=>m.id === selectedModelId\n                        }[\"NodeConfigPanel.useEffect.selectedModel\"]);\n                        // Set reasonable default for maxTokens based on model limits\n                        const defaultMaxTokens = (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.output_token_limit) || (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.context_window) || 4096;\n                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.25))); // Use 25% of max or 1024, whichever is higher\n                        const currentParams = providerConfig.parameters || {};\n                        handleProviderConfigChange('modelId', selectedModelId);\n                        // Only set maxTokens if it's not already set\n                        if (!currentParams.maxTokens) {\n                            handleProviderConfigChange('parameters', {\n                                ...currentParams,\n                                maxTokens: reasonableDefault\n                            });\n                        }\n                    }\n                }\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    const handleProviderConfigChange = (key, value)=>{\n        console.log('[Provider Config Change]', key, '=', value);\n        const currentConfig = config;\n        const newConfig = {\n            ...currentConfig,\n            [key]: value,\n            // Initialize parameters if they don't exist\n            parameters: currentConfig.parameters || {\n                temperature: 1.0,\n                maxTokens: undefined,\n                topP: undefined,\n                frequencyPenalty: undefined,\n                presencePenalty: undefined\n            }\n        };\n        console.log('[Provider Config Change] New config:', newConfig);\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && node.type === 'provider') {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) {\n                    return [];\n                }\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current model's token limits\n    const getCurrentModelLimits = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[getCurrentModelLimits]\": ()=>{\n            if (!fetchedProviderModels || node.type !== 'provider') {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1,\n                    step: 1\n                }; // Default fallback\n            }\n            const providerConfig = config;\n            if (!(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId)) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1,\n                    step: 1\n                }; // Default when no model selected\n            }\n            const currentModel = fetchedProviderModels.find({\n                \"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\": (m)=>m.id === providerConfig.modelId\n            }[\"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\"]);\n            if (!currentModel) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1,\n                    step: 1\n                }; // Default when model not found\n            }\n            // Use output_token_limit if available, otherwise context_window, otherwise default\n            const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;\n            const minTokens = 1;\n            // Calculate appropriate step size based on max tokens\n            let step = 1;\n            if (maxTokens > 50000) step = 1000; // For very large models (64k+), step by 1000\n            else if (maxTokens > 20000) step = 500; // For large models (20k+), step by 500\n            else if (maxTokens > 10000) step = 100; // For medium models (10k+), step by 100\n            else if (maxTokens > 5000) step = 50; // For smaller models (5k+), step by 50\n            else step = 10; // For small models, step by 10\n            return {\n                maxTokens,\n                minTokens,\n                step\n            };\n        }\n    }[\"NodeConfigPanel.useMemo[getCurrentModelLimits]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current max tokens value with proper fallback\n    const currentMaxTokens = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[currentMaxTokens]\": ()=>{\n            var _providerConfig_parameters;\n            if (node.type !== 'provider') return 4096;\n            // Use local state if available (during slider interaction)\n            if (localMaxTokens !== null) {\n                return localMaxTokens;\n            }\n            const providerConfig = config;\n            const configuredValue = providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.maxTokens;\n            if (configuredValue && configuredValue > 0) {\n                return configuredValue;\n            }\n            // Return reasonable default if not configured\n            return Math.min(getCurrentModelLimits.maxTokens, Math.max(1024, Math.floor(getCurrentModelLimits.maxTokens * 0.25)));\n        }\n    }[\"NodeConfigPanel.useMemo[currentMaxTokens]\"], [\n        config,\n        getCurrentModelLimits,\n        node.type,\n        localMaxTokens\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'roleAgent':\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1;\n        const providerConfig = config;\n        console.log('[Render] Current max tokens:', currentMaxTokens);\n        console.log('[Render] Provider config:', providerConfig);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model Variant\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                handleProviderConfigChange('modelId', selectedModelId);\n                                // Update maxTokens based on the selected model\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.25)));\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: reasonableDefault\n                                        });\n                                    }\n                                }\n                            },\n                            disabled: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: \"(0.0 - 2.0)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: getCurrentModelLimits.step,\n                                    value: currentMaxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        console.log('[Max Tokens Slider] Changed to:', value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: getCurrentModelLimits.step,\n                                                    value: currentMaxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-24 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this),\n                (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Role Name\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.roleName || '',\n                            onChange: (e)=>handleConfigChange('roleName', e.target.value),\n                            placeholder: \"e.g., Coder, Writer, Analyst\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Custom Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: config.customPrompt || '',\n                            onChange: (e)=>handleConfigChange('customPrompt', e.target.value),\n                            placeholder: \"Enter custom instructions for this role...\",\n                            rows: 4,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 556,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: config.memoryEnabled || false,\n                                onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-300\",\n                                children: \"Enable memory\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 542,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 605,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 586,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 650,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 694,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 722,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 692,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"C3b7cXDiB/nMng6lyURXM2Mm53M=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});