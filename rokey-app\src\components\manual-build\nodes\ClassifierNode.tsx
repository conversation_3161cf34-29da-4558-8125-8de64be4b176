'use client';

import { CpuChipIcon } from '@heroicons/react/24/outline';
import { NodeProps } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode } from '@/types/manualBuild';

export default function ClassifierNode({ data }: NodeProps<WorkflowNode['data']>) {
  return (
    <BaseNode
      data={data}
      icon={CpuChipIcon}
      color="#3b82f6"
      hasInput={true}
      hasOutput={true}
    >
      <div className="space-y-2">
        <div className="text-sm text-gray-300">
          AI-powered request analysis
        </div>
        <div className="text-xs text-gray-400">
          Analyzes the user request to determine the best routing strategy and extract key information.
        </div>
        {data.config?.classifierType && (
          <div className="mt-2 px-2 py-1 bg-blue-900/30 rounded text-xs text-blue-300">
            Type: {data.config.classifierType}
          </div>
        )}
      </div>
    </BaseNode>
  );
}
