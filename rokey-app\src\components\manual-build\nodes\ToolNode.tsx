'use client';

import { WrenchScrewdriverIcon } from '@heroicons/react/24/outline';
import { NodeProps } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, ToolNodeData } from '@/types/manualBuild';

const toolIcons = {
  google_drive: '📁',
  google_docs: '📄',
  browser: '🌐',
  database: '🗄️',
  api_call: '🔗'
};

const toolNames = {
  google_drive: 'Google Drive',
  google_docs: 'Google Docs',
  browser: 'Browser',
  database: 'Database',
  api_call: 'API Call'
};

export default function ToolNode({ data }: NodeProps<WorkflowNode['data']>) {
  const config = data.config as ToolNodeData['config'];
  const toolType = config?.toolType;
  const toolIcon = toolType ? toolIcons[toolType] : '🔧';
  const toolName = toolType ? toolNames[toolType] : 'External Tool';

  return (
    <BaseNode
      data={data}
      icon={WrenchScrewdriverIcon}
      color="#06b6d4"
      hasInput={true}
      hasOutput={true}
    >
      <div className="space-y-3">
        {toolType ? (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-lg">{toolIcon}</span>
              <span className="text-sm font-medium text-white">
                {toolName}
              </span>
            </div>
            
            {config?.timeout && (
              <div className="text-xs text-gray-400">
                Timeout: {config.timeout}s
              </div>
            )}
            
            <div className="text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded">
              ✓ Tool configured
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              External Tool Integration
            </div>
            <div className="text-xs text-gray-400">
              Connect to external services like Google Drive, databases, APIs, or browser automation.
            </div>
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Needs configuration
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
}
