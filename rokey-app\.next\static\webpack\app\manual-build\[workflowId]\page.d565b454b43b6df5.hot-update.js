"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Load models on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' || node.type === 'vision') {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase\n    ]);\n    // Auto-select first model when provider changes or models load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider' && fetchedProviderModels && fetchedProviderModels.length > 0) {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useEffect.currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useEffect.currentProviderDetails\"]);\n                if (currentProviderDetails && providerConfig.providerId && !providerConfig.modelId) {\n                    let availableModels = [];\n                    if (currentProviderDetails.id === \"openrouter\") {\n                        availableModels = fetchedProviderModels.map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    } else if (currentProviderDetails.id === \"deepseek\") {\n                        const deepseekChatModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekChatModel\"]);\n                        if (deepseekChatModel) {\n                            availableModels.push({\n                                value: \"deepseek-chat\",\n                                label: \"Deepseek V3\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                        const deepseekReasonerModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                        }[\"NodeConfigPanel.useEffect.deepseekReasonerModel\"]);\n                        if (deepseekReasonerModel) {\n                            availableModels.push({\n                                value: \"deepseek-reasoner\",\n                                label: \"DeepSeek R1-0528\",\n                                provider_id: \"deepseek\"\n                            });\n                        }\n                    } else {\n                        availableModels = fetchedProviderModels.filter({\n                            \"NodeConfigPanel.useEffect\": (model)=>model.provider_id === currentProviderDetails.id\n                        }[\"NodeConfigPanel.useEffect\"]).map({\n                            \"NodeConfigPanel.useEffect\": (m)=>({\n                                    value: m.id,\n                                    label: m.display_name || m.name,\n                                    provider_id: m.provider_id\n                                })\n                        }[\"NodeConfigPanel.useEffect\"]).sort({\n                            \"NodeConfigPanel.useEffect\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                        }[\"NodeConfigPanel.useEffect\"]);\n                    }\n                    if (availableModels.length > 0) {\n                        const selectedModelId = availableModels[0].value;\n                        const selectedModel = fetchedProviderModels.find({\n                            \"NodeConfigPanel.useEffect.selectedModel\": (m)=>m.id === selectedModelId\n                        }[\"NodeConfigPanel.useEffect.selectedModel\"]);\n                        // Set reasonable default for maxTokens based on model limits\n                        const defaultMaxTokens = (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.output_token_limit) || (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.context_window) || 4096;\n                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                        const currentParams = providerConfig.parameters || {};\n                        // Update config in a single call to avoid infinite loops\n                        const newConfig = {\n                            ...providerConfig,\n                            modelId: selectedModelId,\n                            parameters: {\n                                ...currentParams,\n                                maxTokens: currentParams.maxTokens || reasonableDefault\n                            }\n                        };\n                        setConfig(newConfig);\n                        onUpdate({\n                            config: newConfig,\n                            isConfigured: isNodeConfigured(node.type, newConfig)\n                        });\n                    }\n                }\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        fetchedProviderModels,\n        node.type,\n        config === null || config === void 0 ? void 0 : config.providerId\n    ]); // Only re-run when provider changes\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    const handleProviderConfigChange = (key, value)=>{\n        const currentConfig = config;\n        const newConfig = {\n            ...currentConfig,\n            [key]: value\n        };\n        // Only initialize parameters if they don't exist and we're setting a parameter\n        if (key === 'parameters' || !currentConfig.parameters) {\n            newConfig.parameters = {\n                temperature: 1.0,\n                maxTokens: undefined,\n                topP: undefined,\n                frequencyPenalty: undefined,\n                presencePenalty: undefined,\n                ...currentConfig.parameters,\n                ...key === 'parameters' ? value : {}\n            };\n        }\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && node.type === 'provider') {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) {\n                    return [];\n                }\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    // Get current model's token limits\n    const getCurrentModelLimits = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[getCurrentModelLimits]\": ()=>{\n            if (!fetchedProviderModels || node.type !== 'provider') {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default fallback\n            }\n            const providerConfig = config;\n            if (!(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId)) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when no model selected\n            }\n            const currentModel = fetchedProviderModels.find({\n                \"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\": (m)=>m.id === providerConfig.modelId\n            }[\"NodeConfigPanel.useMemo[getCurrentModelLimits].currentModel\"]);\n            if (!currentModel) {\n                return {\n                    maxTokens: 4096,\n                    minTokens: 1\n                }; // Default when model not found\n            }\n            // Use output_token_limit if available, otherwise context_window, otherwise default\n            const maxTokens = currentModel.output_token_limit || currentModel.context_window || 4096;\n            const minTokens = 1;\n            return {\n                maxTokens,\n                minTokens\n            };\n        }\n    }[\"NodeConfigPanel.useMemo[getCurrentModelLimits]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'roleAgent':\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1, _providerConfig_parameters2, _providerConfig_parameters3;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || '',\n                            onChange: (e)=>{\n                                const currentConfig = config;\n                                const newConfig = {\n                                    ...currentConfig,\n                                    providerId: e.target.value,\n                                    modelId: '',\n                                    parameters: currentConfig.parameters || {\n                                        temperature: 1.0,\n                                        maxTokens: undefined,\n                                        topP: undefined,\n                                        frequencyPenalty: undefined,\n                                        presencePenalty: undefined\n                                    }\n                                };\n                                setConfig(newConfig);\n                                onUpdate({\n                                    config: newConfig,\n                                    isConfigured: isNodeConfigured(node.type, newConfig)\n                                });\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.apiKey) || '',\n                            onChange: (e)=>handleProviderConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model Variant\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.modelId) || '',\n                            onChange: (e)=>{\n                                const selectedModelId = e.target.value;\n                                // Update maxTokens based on the selected model\n                                let updatedConfig = {\n                                    ...providerConfig,\n                                    modelId: selectedModelId\n                                };\n                                if (selectedModelId && fetchedProviderModels) {\n                                    const selectedModel = fetchedProviderModels.find((m)=>m.id === selectedModelId);\n                                    if (selectedModel) {\n                                        const defaultMaxTokens = selectedModel.output_token_limit || selectedModel.context_window || 4096;\n                                        const reasonableDefault = Math.min(defaultMaxTokens, Math.max(1024, Math.floor(defaultMaxTokens * 0.75)));\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        updatedConfig = {\n                                            ...updatedConfig,\n                                            parameters: {\n                                                ...currentParams,\n                                                maxTokens: reasonableDefault\n                                            }\n                                        };\n                                    }\n                                }\n                                // Single state update to avoid infinite loops\n                                setConfig(updatedConfig);\n                                onUpdate({\n                                    config: updatedConfig,\n                                    isConfigured: isNodeConfigured(node.type, updatedConfig)\n                                });\n                            },\n                            disabled: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) || !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: !(providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: \"Select a provider first\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this) : modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"temperature\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Temperature\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: \"(0.0 - 2.0)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"temperature\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>{\n                                        const temp = parseFloat(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            temperature: temp\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Conservative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                max: \"2\",\n                                                step: \"0.1\",\n                                                value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.temperature) || 1.0,\n                                                onChange: (e)=>{\n                                                    const temp = Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 1.0));\n                                                    const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                    handleProviderConfigChange('parameters', {\n                                                        ...currentParams,\n                                                        temperature: temp\n                                                    });\n                                                },\n                                                className: \"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"maxTokens\",\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: [\n                                \"Max Tokens\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        getCurrentModelLimits.minTokens,\n                                        \" - \",\n                                        getCurrentModelLimits.maxTokens.toLocaleString(),\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"maxTokens\",\n                                    min: getCurrentModelLimits.minTokens,\n                                    max: getCurrentModelLimits.maxTokens,\n                                    step: \"1\",\n                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters2 = providerConfig.parameters) === null || _providerConfig_parameters2 === void 0 ? void 0 : _providerConfig_parameters2.maxTokens) || getCurrentModelLimits.maxTokens,\n                                    onChange: (e)=>{\n                                        const value = parseInt(e.target.value);\n                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                        handleProviderConfigChange('parameters', {\n                                            ...currentParams,\n                                            maxTokens: value\n                                        });\n                                    },\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: getCurrentModelLimits.minTokens,\n                                                    max: getCurrentModelLimits.maxTokens,\n                                                    step: \"1\",\n                                                    value: (providerConfig === null || providerConfig === void 0 ? void 0 : (_providerConfig_parameters3 = providerConfig.parameters) === null || _providerConfig_parameters3 === void 0 ? void 0 : _providerConfig_parameters3.maxTokens) || getCurrentModelLimits.maxTokens,\n                                                    onChange: (e)=>{\n                                                        const value = Math.min(getCurrentModelLimits.maxTokens, Math.max(getCurrentModelLimits.minTokens, parseInt(e.target.value) || getCurrentModelLimits.maxTokens));\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: value\n                                                        });\n                                                    },\n                                                    className: \"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        const currentParams = (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.parameters) || {};\n                                                        handleProviderConfigChange('parameters', {\n                                                            ...currentParams,\n                                                            maxTokens: getCurrentModelLimits.maxTokens\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-orange-400 hover:text-orange-300 underline\",\n                                                    children: \"Max\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Maximum\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, this),\n                (providerConfig === null || providerConfig === void 0 ? void 0 : providerConfig.providerId) === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Role Name\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.roleName || '',\n                            onChange: (e)=>handleConfigChange('roleName', e.target.value),\n                            placeholder: \"e.g., Coder, Writer, Analyst\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 528,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Custom Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: config.customPrompt || '',\n                            onChange: (e)=>handleConfigChange('customPrompt', e.target.value),\n                            placeholder: \"Enter custom instructions for this role...\",\n                            rows: 4,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: config.memoryEnabled || false,\n                                onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-300\",\n                                children: \"Enable memory\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 527,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 571,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 636,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 648,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 635,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 679,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 702,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 707,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 677,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"udGWR3eOtsb7vkhYqHqtgWYEAQw=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});