// Node Types Registry for React Flow
// This file exports all custom node components for the Manual Build workflow editor

import UserRequestNode from './UserRequestNode';
import ClassifierNode from './ClassifierNode';
import ProviderNode from './ProviderNode';
import VisionNode from './VisionNode';
import OutputNode from './OutputNode';
import RoleAgentNode from './RoleAgentNode';
import ConditionalNode from './ConditionalNode';
import MergeNode from './MergeNode';
import LoopNode from './LoopNode';
import ToolNode from './ToolNode';
import MemoryNode from './MemoryNode';
import SwitchNode from './SwitchNode';

// Export all node types for React Flow
export const nodeTypes = {
  userRequest: UserRequestNode,
  classifier: ClassifierNode,
  provider: ProviderNode,
  vision: VisionNode,
  output: OutputNode,
  roleAgent: RoleAgentNode,
  conditional: ConditionalNode,
  merge: MergeNode,
  loop: LoopN<PERSON>,
  tool: ToolNode,
  memory: MemoryNode,
  switch: SwitchNode,
};

// Export individual components
export {
  UserRequestNode,
  ClassifierNode,
  ProviderNode,
  VisionNode,
  OutputNode,
  RoleAgentNode,
  ConditionalNode,
  MergeNode,
  LoopNode,
  ToolNode,
  MemoryNode,
  SwitchNode,
};
