// Node Types Registry for React Flow
// This file exports all custom node components for the Manual Build workflow editor

import UserRequestNode from './UserRequestNode';
import ClassifierNode from './ClassifierNode';
import ProviderNode from './ProviderNode';
import OutputNode from './OutputNode';
import RoleAgentNode from './RoleAgentNode';
import ConditionalNode from './ConditionalNode';
import MergeNode from './MergeNode';
import LoopN<PERSON> from './LoopNode';
import ToolNode from './ToolNode';
import MemoryNode from './MemoryNode';
import SwitchNode from './SwitchNode';

// Export all node types for React Flow
export const nodeTypes = {
  userRequest: UserRequestNode,
  classifier: ClassifierNode,
  provider: ProviderNode,
  output: OutputNode,
  roleAgent: RoleAgentNode,
  conditional: ConditionalNode,
  merge: MergeNode,
  loop: LoopNode,
  tool: ToolNode,
  memory: MemoryNode,
  switch: SwitchNode,
};

// Export individual components
export {
  UserRequestNode,
  ClassifierNode,
  ProviderNode,
  OutputNode,
  RoleAgentNode,
  ConditionalNode,
  MergeNode,
  LoopNode,
  ToolNode,
  MemoryNode,
  SwitchNode,
};
