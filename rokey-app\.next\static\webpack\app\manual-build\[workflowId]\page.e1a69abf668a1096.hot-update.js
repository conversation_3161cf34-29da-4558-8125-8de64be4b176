"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Specialized AI agent with specific role',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {\n                        roleId: '',\n                        roleName: '',\n                        roleType: 'predefined',\n                        customPrompt: '',\n                        memoryEnabled: false\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    logic: {\n        label: 'Logic & Control',\n        description: 'Flow control and decision making',\n        nodes: [\n            {\n                type: 'conditional',\n                label: 'Conditional',\n                description: 'Branch workflow based on conditions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Conditional',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'merge',\n                label: 'Merge',\n                description: 'Combine multiple inputs',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Merge',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'switch',\n                label: 'Switch',\n                description: 'Route to different paths',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Switch',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'loop',\n                label: 'Loop',\n                description: 'Repeat operations',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Loop',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tool',\n                description: 'External tool integration',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tool',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});