'use client';

import { CircleStackIcon } from '@heroicons/react/24/outline';
import { NodeProps } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode } from '@/types/manualBuild';

export default function MemoryNode({ data }: NodeProps<WorkflowNode['data']>) {
  const config = data.config;
  const memoryType = config?.memoryType;
  const storageKey = config?.storageKey;

  return (
    <BaseNode
      data={data}
      icon={CircleStackIcon}
      color="#ec4899"
      hasInput={true}
      hasOutput={true}
    >
      <div className="space-y-3">
        {memoryType ? (
          <div className="space-y-2">
            <div className="text-sm font-medium text-white">
              {memoryType === 'store' ? 'Store Data' : 'Retrieve Data'}
            </div>
            
            {storageKey && (
              <div className="text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded font-mono">
                Key: {storageKey}
              </div>
            )}
            
            <div className="text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded">
              ✓ Memory configured
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              Memory Storage
            </div>
            <div className="text-xs text-gray-400">
              Store and retrieve data across workflow executions for context persistence.
            </div>
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Needs configuration
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
}
