'use client';

import { Handle, Position } from '@xyflow/react';
import { ReactNode } from 'react';
import { WorkflowNode } from '@/types/manualBuild';

interface BaseNodeProps {
  data: WorkflowNode['data'];
  children?: ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  hasInput?: boolean;
  hasOutput?: boolean;
  hasRoleInput?: boolean;
  inputLabel?: string;
  outputLabel?: string;
  roleInputLabel?: string;
  className?: string;
}

export default function BaseNode({
  data,
  children,
  icon: Icon,
  color = '#ff6b35',
  hasInput = true,
  hasOutput = true,
  hasRoleInput = false,
  inputLabel = 'Input',
  outputLabel = 'Output',
  roleInputLabel = 'Role',
  className = ''
}: BaseNodeProps) {
  const isConfigured = data.isConfigured;
  const hasError = data.hasError;

  return (
    <div className={`relative ${className}`}>
      {/* Input Handle */}
      {hasInput && (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
          className="w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors"
          style={{ left: -8, top: '50%' }}
        />
      )}

      {/* Role Input Handle */}
      {hasRoleInput && (
        <Handle
          type="target"
          position={Position.Left}
          id="role"
          className="w-3 h-3 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors"
          style={{ left: -6, top: '25%' }}
        />
      )}

      {/* Node Body */}
      <div
        className={`min-w-[200px] rounded-lg border-2 transition-all duration-200 ${
          hasError
            ? 'border-red-500 bg-red-900/20'
            : isConfigured
            ? 'border-gray-600 bg-gray-800/90'
            : 'border-yellow-500 bg-yellow-900/20'
        } backdrop-blur-sm shadow-lg hover:shadow-xl`}
        style={{
          borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'
        }}
      >
        {/* Header */}
        <div 
          className="px-4 py-3 rounded-t-lg flex items-center gap-3"
          style={{
            background: hasError 
              ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))'
              : `linear-gradient(135deg, ${color}20, ${color}10)`
          }}
        >
          {Icon && (
            <div 
              className="p-2 rounded-lg"
              style={{
                backgroundColor: hasError ? '#ef444420' : `${color}20`,
                color: hasError ? '#ef4444' : color
              }}
            >
              <Icon className="w-4 h-4" />
            </div>
          )}
          <div className="flex-1">
            <div className="font-medium text-white text-sm">
              {data.label}
            </div>
            {data.description && (
              <div className="text-xs text-gray-400 mt-1">
                {data.description}
              </div>
            )}
          </div>
          
          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            {hasError ? (
              <div className="w-2 h-2 bg-red-500 rounded-full" title="Error" />
            ) : isConfigured ? (
              <div className="w-2 h-2 bg-green-500 rounded-full" title="Configured" />
            ) : (
              <div className="w-2 h-2 bg-yellow-500 rounded-full" title="Needs configuration" />
            )}
          </div>
        </div>

        {/* Content */}
        {children && (
          <div className="px-4 py-3 border-t border-gray-700/50">
            {children}
          </div>
        )}

        {/* Error Message */}
        {hasError && data.errorMessage && (
          <div className="px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg">
            <div className="text-xs text-red-300">
              {data.errorMessage}
            </div>
          </div>
        )}
      </div>

      {/* Output Handle */}
      {hasOutput && (
        <Handle
          type="source"
          position={Position.Right}
          className="w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors"
          style={{ right: -8 }}
        />
      )}
    </div>
  );
}
