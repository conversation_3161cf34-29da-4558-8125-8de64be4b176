"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChevronDownIcon,ChevronRightIcon,CircleStackIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Specialized AI agent with specific role',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    logic: {\n        label: 'Logic & Control',\n        description: 'Flow control and decision making',\n        nodes: [\n            {\n                type: 'conditional',\n                label: 'Conditional',\n                description: 'Branch workflow based on conditions',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Conditional',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'merge',\n                label: 'Merge',\n                description: 'Combine multiple inputs',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Merge',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'switch',\n                label: 'Switch',\n                description: 'Route to different paths',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Switch',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'loop',\n                label: 'Loop',\n                description: 'Repeat operations',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Loop',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tool',\n                description: 'External tool integration',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tool',\n                    config: {},\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data',\n                icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {},\n                    isConfigured: false\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BoltIcon_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});