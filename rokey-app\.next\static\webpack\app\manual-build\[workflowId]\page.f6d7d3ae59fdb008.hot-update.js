"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx":
/*!************************************************************!*\
  !*** ./src/components/manual-build/nodes/ProviderNode.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst providerColors = {\n    openai: '#10b981',\n    anthropic: '#f97316',\n    google: '#3b82f6',\n    deepseek: '#8b5cf6',\n    xai: '#374151',\n    openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'\n};\nconst providerNames = {\n    openai: 'OpenAI',\n    anthropic: 'Anthropic',\n    google: 'Google',\n    deepseek: 'DeepSeek',\n    xai: 'xAI (Grok)',\n    openrouter: 'OpenRouter'\n};\nfunction ProviderNode(param) {\n    let { data, id } = param;\n    _s();\n    const { getEdges, getNodes } = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useReactFlow)();\n    const config = data.config;\n    const providerId = config === null || config === void 0 ? void 0 : config.providerId;\n    const modelId = config === null || config === void 0 ? void 0 : config.modelId;\n    const color = providerId ? providerColors[providerId] : '#ff6b35';\n    const providerName = providerId ? providerNames[providerId] : 'AI Provider';\n    // Get connected role nodes\n    const connectedRoles = getEdges().filter((edge)=>edge.target === id && edge.targetHandle === 'role').map((edge)=>{\n        // Find the source node to get role information\n        const sourceNode = getNodes().find((node)=>node.id === edge.source);\n        if (sourceNode && sourceNode.type === 'roleAgent') {\n            const roleConfig = sourceNode.data.config;\n            return {\n                id: edge.source,\n                name: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleName) || 'Unknown Role',\n                type: (roleConfig === null || roleConfig === void 0 ? void 0 : roleConfig.roleType) || 'predefined'\n            };\n        }\n        return null;\n    }).filter(Boolean);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CloudIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: typeof color === 'string' ? color : '#ff6b35',\n        hasInput: true,\n        hasOutput: true,\n        hasRoleInput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: providerId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: providerName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            providerId === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-0.5 rounded-full\",\n                                children: \"300+ Models\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this),\n                    modelId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded\",\n                        children: [\n                            \"Model: \",\n                            modelId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.parameters) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Temp: \",\n                                    config.parameters.temperature || 1.0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Max: \",\n                                    config.parameters.maxTokens || 'Auto'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 15\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.fallbackProvider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: [\n                            \"Fallback: \",\n                            providerNames[config.fallbackProvider.providerId]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, this),\n                    connectedRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: \"Roles:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1\",\n                                children: connectedRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30\",\n                                        children: role.name\n                                    }, role.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 63,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"AI Provider Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Configure to connect to OpenAI, Anthropic, Google, DeepSeek, xAI, or OpenRouter.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n                lineNumber: 116,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ProviderNode.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(ProviderNode, \"RZ7Xaupw8bsRjC9QeKwHIIgcTfo=\", false, function() {\n    return [\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useReactFlow\n    ];\n});\n_c = ProviderNode;\nvar _c;\n$RefreshReg$(_c, \"ProviderNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ProviderNode.tsx\n"));

/***/ })

});