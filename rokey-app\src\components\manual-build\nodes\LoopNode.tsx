'use client';

import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { NodeProps } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode } from '@/types/manualBuild';

export default function LoopNode({ data }: NodeProps<WorkflowNode['data']>) {
  const config = data.config;
  const loopType = config?.loopType;
  const maxIterations = config?.maxIterations;

  return (
    <BaseNode
      data={data}
      icon={ArrowPathIcon}
      color="#f59e0b"
      hasInput={true}
      hasOutput={true}
    >
      <div className="space-y-2">
        {loopType ? (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              Type: {loopType}
            </div>
            {maxIterations && (
              <div className="text-xs text-gray-400">
                Max iterations: {maxIterations}
              </div>
            )}
            <div className="text-xs text-amber-300 bg-amber-900/20 px-2 py-1 rounded">
              ⚠️ Use with caution - can cause infinite loops
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              Loop Operations
            </div>
            <div className="text-xs text-gray-400">
              Repeat operations based on conditions or iterations.
            </div>
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Needs configuration
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
}
